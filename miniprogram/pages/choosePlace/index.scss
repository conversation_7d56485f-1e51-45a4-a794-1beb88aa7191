@import '../miniprogram/resource/scss/index.scss';

.container {
  padding: 20px;
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* 表单卡片样式 */
.form-card {
  background: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  padding: 32rpx;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 1px solid #f5f5f5;
}

.card-icon {
  font-size: 36rpx;
  margin-right: 16rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

/* 位置相关样式优化 */
.location-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 1px solid #e9ecef;
}

.location-btn {
  padding: 12rpx;
  background: #1890ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

.location-icon {
  width: 24px;
  height: 24px;
  filter: brightness(0) invert(1);
}

/* 定位动画样式 */
.location-container {
  display: flex;
  align-items: center;
  position: relative;
}

.location-animation {
  position: relative;
  width: 20px;
  height: 20px;
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.location-pulse {
  position: absolute;
  width: 20px;
  height: 20px;
  border: 2px solid #1890ff;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.location-dot {
  width: 8px;
  height: 8px;
  background-color: #1890ff;
  border-radius: 50%;
  position: relative;
  z-index: 1;
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

/* 定位中的文字样式 */
.address.locating {
  color: #1890ff;
  font-style: italic;
}

/* 定位按钮旋转动画 */
.rotating {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
/* 信息横幅样式 */
.info-banner {
  margin: 10px;
  padding: 20rpx;
  background: linear-gradient(135deg, #fff3e0, #ffe0b2);
  border-radius: 16rpx;
  border-left: 4px solid #ff9800;
  box-shadow: 0 2px 8px rgba(255, 152, 0, 0.1);
}

.info-header {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.info-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.info-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #e65100;
}

.info-content {
  padding-left: 44rpx;
}

.info-item {
  display: flex;
  margin-bottom: 8rpx;
  font-size: 28rpx;
}

.info-label {
  color: #f57c00;
  font-weight: 500;
  min-width: 140rpx;
}

.info-value {
  color: #bf360c;
  flex: 1;
}

.title {
  text-align: center;
  font-size: 18px;
  margin-bottom: 20px;
}

.form-item {
  margin-bottom: 15px;
}

.form-item1 {
  margin-bottom: 15px;
  display: flex;
  align-items: baseline; /* 下标对齐 */
}

/* 剩余额度卡片 */
.quota-card {
  background: linear-gradient(135deg, #e8f5e8, #f0f8f0);
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  padding: 32rpx;
  border-left: 4px solid #52c41a;
  box-shadow: 0 2px 8px rgba(82, 196, 26, 0.1);
}

.quota-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.quota-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.quota-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #389e0d;
}

.quota-content {
  display: flex;
  gap: 32rpx;
}

.quota-item {
  display: flex;
  align-items: center;
}

.quota-label {
  font-size: 28rpx;
  color: #52c41a;
  margin-right: 8rpx;
}

.quota-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #237804;
  background: rgba(82, 196, 26, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
}

label {
  display: block;
  margin-bottom: 2px;
  font-size: 15px;
  color: var(--text-color);
}

.mb-16 {
  display: block;
  margin-bottom: 25px;
  font-size: 16px;
  color: var(--text-color);
}

t-input,
t-select {
  width: 100%;
}

.picker-wrapper {
  display: flex;
  align-items: center;
}

.picker-icon {
  margin-left: 10px;
  font-size: 18px;
  cursor: pointer;
}

/* 门头卡片样式 */
.door-card {
  background: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  overflow: hidden;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
  }
}

.door-header {
  background: linear-gradient(135deg, #667eea, #764ba2);
  padding: 24rpx 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.door-number {
  font-size: 32rpx;
  font-weight: 600;
  color: white;
}

.door-status {
  background: rgba(255, 255, 255, 0.2);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: white;
  backdrop-filter: blur(10px);
}

.door-content {
  padding: 32rpx;
}

/* 提交按钮样式 */
.submit-container {
  margin-top: 40rpx;
  padding: 0 32rpx 40rpx;
}

.submit-btn {
  background: linear-gradient(135deg, #1890ff, #096dd9) !important;
  border-radius: 16rpx !important;
  height: 96rpx !important;
  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.3) !important;
  border: none !important;

  &:active {
    transform: translateY(2px);
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.4) !important;
  }
}

.submit-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
}

.submit-icon {
  font-size: 32rpx;
}

.submit-text {
  font-size: 32rpx;
  font-weight: 600;
  color: white;
}

.textarea-container {
  margin-top: 10px;
  --td-textarea-placeholder-color: rgb(211, 210, 210);
}

.textarea-label {
  font-size: 12px;
  font-weight: bold;
  color: rgb(185, 34, 34);
  margin-bottom: 5px;
}

.textarea-custom {
  border: 1px solid rgb(104, 103, 103);
  padding: 10px;
  border-radius: 5px;
  font-size: 14px;
  line-height: 1.5;
  color: rgb(0, 0, 0);
  height: 130px;
  --td-textarea-disabled-text-color: rgb(0, 0, 0);
}

/* 数量控制卡片 */
.quantity-card {
  background: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  padding: 32rpx;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
}

.quantity-control {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 32rpx;
  margin-top: 16rpx;
}

.quantity-btn {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &.decrease {
    background: linear-gradient(135deg, #ff7875, #ff4d4f);
    color: white;

    &:active {
      transform: scale(0.95);
      box-shadow: 0 1px 4px rgba(255, 77, 79, 0.4);
    }
  }

  &.increase {
    background: linear-gradient(135deg, #73d13d, #52c41a);
    color: white;

    &:active {
      transform: scale(0.95);
      box-shadow: 0 1px 4px rgba(82, 196, 26, 0.4);
    }
  }
}

.btn-text {
  font-size: 40rpx;
  line-height: 1;
}

.quantity-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: 120rpx;
}

.quantity-number {
  font-size: 48rpx;
  font-weight: 700;
  color: #1890ff;
  line-height: 1;
}

.quantity-unit {
  font-size: 24rpx;
  color: #8c8c8c;
  margin-top: 4rpx;
}
  align-items: center;
}

.quantity-btn {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
}

.quantity-display {
  margin: 0 5px;
  font-size: 16px;
}

.address {
  flex-grow: 1; /* 占满剩余空间 */
  color: #333; /* 内容颜色 */
  font-size: 14px; /* 内容字号 */
  word-break: break-word; /* 自动换行 */
}

.block-number {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 20px;
  border-radius: 5px;
}

.title1 {
  margin-top: 10px;
  font-size: 15px;
  margin-bottom: 20px;
}

.title-container {
  display: flex;
  align-items: center;
  margin-top: 10px;
  margin-bottom: 10px;
  align-items: baseline; /* 下标对齐 */
}

.titlePart1 {
  font-size: 16px;
  color: #333;
}

.titlePart3 {
  font-size: 14px;
  color: #888888; /* 灰色字体 */
  margin-left: 8px; /* 左侧间距 */
}

.titlePart2 {
  font-size: 14px;
  color: red;
  margin-left: 8px;
}

.radio-group-inline {
  display: flex;
  justify-content: space-between; /* 将两个 radio 项目分布在一行 */
  margin-top: 10px;
}

.radio-item {
  display: flex;
  align-items: center;
}

.radio-item radio {
  margin-right: 5px;
}
