<view class="info-banner">
  <view class="info-header">
    <view class="info-icon">📋</view>
    <view class="info-title">提交规则说明</view>
  </view>
  <view class="info-content">
    <view class="info-item">
      <view class="info-label">门头制作：</view>
      <view class="info-value">不超过2个，需一次之内提交</view>
    </view>
    <view class="info-item">
      <view class="info-label">非门头制作：</view>
      <view class="info-value">不超过4个，可分次提交</view>
    </view>
  </view>
</view>
<view class="container">
  <!-- 基础信息卡片 -->
  <view class="form-card">
    <view class="card-header">
      <view class="card-icon">📍</view>
      <view class="card-title">基础信息</view>
    </view>

    <!-- 位置展示 -->
    <view class="form-item">
      <view class="labelGroup">
        <label class="redLab">*</label>
        <label>当前位置</label>
      </view>
      <view class="location-wrapper">
        <!-- 定位动画 -->
        <view class="location-container">
          <view class="location-animation" wx:if="{{isLocating}}">
            <view class="location-pulse"></view>
            <view class="location-dot"></view>
          </view>
          <text class="address {{isLocating ? 'locating' : ''}}">
            {{isLocating ? '正在定位...' : (address || '点击获取位置')}}
          </text>
        </view>
        <view class="location-btn" bindtap="reLocate">
          <image
            src="../../resource/img/locate.png"
            class="location-icon {{isLocating ? 'rotating' : ''}}"
          />
        </view>
      </view>
    </view>

    <!-- 活动类型选择 -->
    <view class="form-item">
      <view class="labelGroup">
        <label class="redLab">*</label>
        <t-cell
          class="mb-16"
          title="选择活动类型"
          arrow
          hover
          note="{{activityTypeText}}"
          bindtap="onActivityTypePicker"
        />
        <t-picker
          visible="{{activityTypeVisible}}"
          value="{{activityTypeValue}}"
          data-key="activityType"
          title="选择活动类型"
          cancelBtn="取消"
          confirmBtn="确认"
          bindchange="onPickerChange"
          bindpick="onColumnChange"
          bindcancel="onPickerCancel"
        >
          <t-picker-item options="{{activityTypes}}" />
        </t-picker>
      </view>
    </view>

    <!-- 广告公司选择 -->
    <view class="form-item">
      <view class="labelGroup">
        <label class="redLab">*</label>
        <label>广告公司</label>
      </view>
      <view class="picker-wrapper" bindtap="navigateToAdCompanyPicker">
        <t-input placeholder="请选择广告公司" value="{{adCompany}}" disabled />
        <view class="picker-icon">🔍</view>
      </view>
    </view>

    <!-- 安装门头店铺选择 -->
    <view class="form-item">
      <view class="labelGroup">
        <label class="redLab">*</label>
        <label>安装门头的店铺</label>
      </view>
      <view class="picker-wrapper" bindtap="navigateToShopPicker">
        <t-input placeholder="请选择终端网点" value="{{shop}}" disabled />
        <view class="picker-icon">🔍</view>
      </view>
    </view>
  </view>

  <!-- 剩余额度提示 -->
  <view wx:if="{{countFlag}}" class="quota-card">
    <view class="quota-header">
      <view class="quota-icon">💰</view>
      <view class="quota-title">剩余额度</view>
    </view>
    <view class="quota-content">
      <view class="quota-item">
        <view class="quota-label">门头数：</view>
        <view class="quota-value">{{menTouNum}}个</view>
      </view>
      <view class="quota-item">
        <view class="quota-label">非门头数：</view>
        <view class="quota-value">{{notMenTouNum}}个</view>
      </view>
    </view>
  </view>

  <!-- 数量控制卡片 -->
  <view class="quantity-card">
    <view class="card-header">
      <view class="card-icon">🔢</view>
      <view class="card-title">制作数量</view>
    </view>
    <view class="quantity-control">
      <button class="quantity-btn decrease" bindtap="decreaseQuantity">
        <text class="btn-text">-</text>
      </button>
      <view class="quantity-display">
        <text class="quantity-number">{{quantity}}</text>
        <text class="quantity-unit">个</text>
      </view>
      <button class="quantity-btn increase" bindtap="increaseQuantity">
        <text class="btn-text">+</text>
      </button>
    </view>
  </view>

  <!-- 门头列表 -->
  <block wx:for="{{doors}}" wx:key="index">
    <view class="door-card">
      <view class="door-header">
        <view class="door-number">第 {{ index + 1 }} 个</view>
        <view class="door-status">{{item.adType === 1 ? '门头' : '非门头'}}</view>
      </view>
      <view class="door-content">
        <view class="form-item">
          <view class="title-container">
            <label class="redLab">*</label>
            <view class="titlePart1">制作类型</view>
            <!-- <view class="titlePart2">(门头广告面积选填)</view> -->
          </view>
          <!-- 单选框部分 -->
          <view class="radio-container">
            <radio-group
              class="radio-group-inline"
              data-index="{{index}}"
              bindchange="onAdTypeRadioChange"
              value="{{item.adType}}"
            >
              <label class="radio-item">
                <radio value="门头" checked="{{item.adType === 1}}" checked="true" />
                门头
              </label>
              <label class="radio-item">
                <radio value="非门头" checked="{{item.adType === 2}}" />
                非门头
              </label>
            </radio-group>
          </view>
        </view>
        <!-- 广告位置选择 -->
        <view class="form-item1">
          <label class="redLab">*</label>
          <t-cell
            class="mb-16 custom-bg"
            title="选择广告位置"
            arrow
            hover
            note="{{item.adPositionValue}}"
            data-index="{{index}}"
            bindtap="onAdPositionPicker"
          />
          <t-picker
            class="custom-bg"
            visible="{{item.adPositionVisible}}"
            value="{{item.adPositionValue}}"
            data-index="{{index}}"
            data-key="adPosition"
            title="选择广告位置"
            cancelBtn="取消"
            confirmBtn="确认"
            bindchange="onPickerChange"
            bindpick="onColumnChange"
            bindcancel="onPickerCancel"
          >
            <t-picker-item options="{{adPositionOptions}}" />
          </t-picker>
        </view>

        <view class="wrapper">
          <view class="title-container">
            <label class="redLab">*</label>
            <view class="titlePart1">照片</view>
            <view class="titlePart3">远景+近景+其他</view>
          </view>
          <t-upload
            mediaType="{{['image']}}"
            files="{{item.photos}}"
            max="3"
            data-index="{{index}}"
            grid-config="{{gridConfig}}"
            bind:add="handleAdd"
            bind:remove="handleRemove"
          ></t-upload>
        </view>

        <view class="wrapper">
          <view class="title-container">
            <label class="redLab">*</label>
            <view class="titlePart1">小视频</view>
            <view class="titlePart3">360°转一圈</view>
          </view>
          <t-upload
            mediaType="{{['video']}}"
            max="1"
            data-index="{{index}}"
            files="{{item.video}}"
            grid-config="{{gridConfig}}"
            bind:add="handleVideoAdd"
            bind:remove="handleVideoRemove"
          ></t-upload>
        </view>

        <view class="form-item">
          <view class="title-container">
            <label class="redLab">*</label>
            <view class="titlePart1">广告发布地址</view>
            <!-- <view class="titlePart2">(门头广告面积选填)</view> -->
          </view>
          <!-- 单选框部分 -->
          <view class="radio-container">
            <radio-group
              class="radio-group-inline"
              data-index="{{index}}"
              bindchange="onRadioChange"
              value="{{item.addressType}}"
            >
              <label class="radio-item">
                <radio value="现场选址" checked="{{item.addressType === '现场选址'}}" checked="true" />
                现场选址
              </label>
              <label class="radio-item">
                <radio value="非现场选址" checked="{{item.addressType === '非现场选址'}}" />
                非现场选址
              </label>
            </radio-group>
          </view>
          <view class="textarea-container">
            <t-textarea
              class="textarea-custom"
              placeholder="1.填写实际的广告发布地址\n2.门头的面积：可以大致填写"
              data-index="{{index}}"
              value="{{item.adAddress}}"
              disableDefaultPadding="{{true}}"
              autosize="{{autosize}}"
              disabled="{{item.addressType === '现场选址'}}"
              bind:change="onAdAddressInput"
            />
          </view>
        </view>
      </view>
    </view>
  </block>

  <!-- 提交按钮 -->
  <view class="submit-container">
    <t-button class="submit-btn" block theme="primary" bindtap="submit">
      <view class="submit-content">
        <text class="submit-icon">📤</text>
        <text class="submit-text">提交申请</text>
      </view>
    </t-button>
  </view>
</view>
